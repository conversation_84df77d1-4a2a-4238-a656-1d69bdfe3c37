# TradingAgents/graph/setup.py

from typing import Dict, Any
from langchain_openai import Chat<PERSON>penA<PERSON>
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import ToolNode

from trading.agent.analysts.bear_analyst import create_bear_researcher
from trading.agent.analysts.bull_analyst import create_bull_researcher
from trading.agent.analysts.fundamental_analyst import create_fundamentals_analyst
from trading.agent.analysts.research_manager import create_research_manager
from trading.agent.analysts.sentiment_analyst import create_news_analyst
from trading.src.next_step import nextStep
from trading.utils.state import AgentState
from trading.utils.tools import Toolkit, create_msg_delete



class GraphSetup:
    """Handles the setup and configuration of the agent graph."""

    def __init__(
        self,
        quick_thinking_llm: ChatOpenAI,
        deep_thinking_llm: ChatOpenAI,
        toolkit: Toolkit,
        tool_nodes: Dict[str, ToolNode],
        bull_memory,
        bear_memory,
        # trader_memory,
        invest_judge_memory,
        # risk_manager_memory,
        conditional_logic: nextStep,
    ):
        """Initialize with required components."""
        self.quick_thinking_llm = quick_thinking_llm
        self.deep_thinking_llm = deep_thinking_llm
        self.toolkit = toolkit
        self.tool_nodes = tool_nodes
        self.bull_memory = bull_memory
        self.bear_memory = bear_memory
        # self.trader_memory = trader_memory
        self.invest_judge_memory = invest_judge_memory
        # self.risk_manager_memory = risk_manager_memory
        self.conditional_logic = conditional_logic

    def setup_graph(self):
        """Set up and compile the agent workflow graph with fundamentals and news analysts."""
        
        # Create analyst nodes - always include fundamentals and news
        analyst_nodes = {}
        delete_nodes = {}
        tool_nodes = {}

        # # Always create news analyst
        analyst_nodes["news"] = create_news_analyst(
            self.quick_thinking_llm, self.toolkit
        )
        delete_nodes["news"] = create_msg_delete()
        tool_nodes["news"] = self.tool_nodes["news"]

        # Always create fundamentals analyst
        analyst_nodes["fundamentals"] = create_fundamentals_analyst(
            self.quick_thinking_llm, self.toolkit
        )
        delete_nodes["fundamentals"] = create_msg_delete()
        tool_nodes["fundamentals"] = self.tool_nodes["fundamentals"]

        # Create researcher and manager nodes
        bull_researcher_node = create_bull_researcher(
            self.quick_thinking_llm, self.bull_memory
        )
        bear_researcher_node = create_bear_researcher(
            self.quick_thinking_llm, self.bear_memory
        )
        
        research_manager_node = create_research_manager(
            self.deep_thinking_llm, self.invest_judge_memory
        )

        # Create workflow
        workflow = StateGraph(AgentState)

        # # Add analyst nodes to the graph
        workflow.add_node("News Analyst", analyst_nodes["news"])
        workflow.add_node("Msg Clear News", delete_nodes["news"])
        workflow.add_node("tools_news", tool_nodes["news"])
        
        workflow.add_node("Fundamentals Analyst", analyst_nodes["fundamentals"])
        workflow.add_node("Msg Clear Fundamentals", delete_nodes["fundamentals"])
        workflow.add_node("tools_fundamentals", tool_nodes["fundamentals"])

        # Add other nodes
        workflow.add_node("Bull Researcher", bull_researcher_node)
        workflow.add_node("Bear Researcher", bear_researcher_node)
        
        workflow.add_node("Research Manager", research_manager_node)

        # Define edges - fixed sequence: news -> fundamentals -> Bull Researcher
        workflow.add_edge(START, "Fundamentals Analyst")

        # Fundamentals analyst flow
        workflow.add_conditional_edges(
            "Fundamentals Analyst",
            getattr(self.conditional_logic, "should_continue_fundamentals"),
            ["tools_fundamentals", "Msg Clear Fundamentals"],
        )
        workflow.add_edge("tools_fundamentals", "Fundamentals Analyst")
        workflow.add_edge("Msg Clear Fundamentals", "News Analyst")
        # workflow.add_edge("Msg Clear Fundamentals", "News Analyst")

        # # News analyst flow
        workflow.add_conditional_edges(
            "News Analyst",
            getattr(self.conditional_logic, "should_continue_news"),
            ["tools_news", "Msg Clear News"],
        )
        workflow.add_edge("tools_news", "News Analyst")
        workflow.add_edge("Msg Clear News", "Bull Researcher")

        # Add remaining edges
        workflow.add_conditional_edges(
            "Bull Researcher",
            self.conditional_logic.should_continue_debate,
            {
                "Bear Researcher": "Bear Researcher",
                # "End":END
                "Research Manager": "Research Manager",
            },
        )
        workflow.add_conditional_edges(
            "Bear Researcher",
            self.conditional_logic.should_continue_debate,
            {
                "Bull Researcher": "Bull Researcher",
                # "End":END
                "Research Manager": "Research Manager",
            },
        )
        workflow.add_edge("Research Manager", END)

        # workflow.add_edge("Risk Judge", END)

        # Compile and return
        return workflow.compile()
