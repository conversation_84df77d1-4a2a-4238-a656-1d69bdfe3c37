from langchain_core.messages import BaseMessage, HumanMessage, ToolMessage, AIMessage
from typing import List
from typing import Annotated
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.messages import RemoveMessage
from langchain_core.tools import tool
from datetime import date, timedelta, datetime
import functools
import pandas as pd
import os
from dateutil.relativedelta import relativedelta
from langchain_openai import ChatOpenAI
from trading.data.news_crawler import get_sentiment, get_stock_news
from trading.default_config import DEFAULT_CONFIG
from langchain_core.messages import HumanMessage
from trading.data.fundamental_data import get_financial_metrics


def create_msg_delete():
    def delete_messages(state):
        """Clear messages and add placeholder for Anthropic compatibility"""
        messages = state["messages"]
        
        # Remove all messages
        removal_operations = [RemoveMessage(id=m.id) for m in messages]
        
        # Add a minimal placeholder message
        placeholder = HumanMessage(content="Continue")
        
        return {"messages": removal_operations + [placeholder]}
    
    return delete_messages


class Toolkit:
    _config = DEFAULT_CONFIG.copy()
    _search_engines = {}  # 缓存搜索引擎实例

    @classmethod
    def update_config(cls, config):
        """Update the class-level configuration."""
        cls._config.update(config)

    @property
    def config(self):
        """Access the configuration."""
        return self._config

    def __init__(self, config=None):
        if config:
            self.update_config(config)

    @classmethod
    def _get_search_engine(cls, engine: str):
        """获取或创建搜索引擎实例（单例模式）"""
        if engine not in cls._search_engines:
            from trading.data.search_engine import SearchEngine
            cls._search_engines[engine] = SearchEngine(engine=engine)
        return cls._search_engines[engine]

    @staticmethod
    @tool
    def search_data(
        keywords: str, max_results: int = 10, save_to_json: str = None, engine: str = "tavily"
    ):
        """
        Search for information using various search engines (DuckDuckGo, Tavily).
        
        Args:
            keywords (str): Search keywords/query
            max_results (int): Maximum number of results to return (default: 10)
            save_to_json (str): Optional filename to save results as JSON
            engine (str): Search engine to use - "ddg" (DuckDuckGo) or "tavily" (default: "ddg")
        
        Returns:
            List[Dict]: Search results with title, url, and description fields
        """
        try:
            # 使用缓存的搜索引擎实例
            search_engine = Toolkit._get_search_engine(engine)
            
            # Perform search
            results = search_engine.search(
                keywords=keywords,
                max_results=max_results,
                save_to_json=save_to_json
            )
            
            return results
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return []

    @staticmethod
    @tool
    def get_fundamentals(symbol: str):
        """
        Retrieve fundamental financial metrics for a given stock symbol using offline data sources.
        
        This function provides comprehensive financial analysis including profitability ratios,
        growth indicators, financial health metrics, and valuation ratios for investment decision making.
        
        Args:
            symbol (str): Stock symbol/ticker of the company (e.g., "600000", "000001")
        
        Returns:
            List[Dict]: A list containing financial metrics dictionary with the following key indicators:
                - return_on_equity: Net asset return rate (%)
                - net_margin: Net profit margin (%)
                - operating_margin: Operating profit margin (%)
                - revenue_growth: Revenue growth rate (%)
                - earnings_growth: Net profit growth rate (%)
                - book_value_growth: Net asset growth rate (%)
                - current_ratio: Current ratio for liquidity analysis
                - debt_to_equity: Asset-liability ratio (%)
                - free_cash_flow_per_share: Free cash flow per share
                - earnings_per_share: Earnings per share
                - pe_ratio: Price-to-earnings ratio
                - price_to_book: Price-to-book ratio
                - price_to_sales: Price-to-sales ratio
        
        Example:
            >>> toolkit = Toolkit()
            >>> metrics = toolkit.get_fundamentals("600000")
            >>> print(metrics[0]['return_on_equity'])  # Access ROE
        """
        return get_financial_metrics(symbol);



    @staticmethod
    @tool
    def get_sentiments(symbol: str, max_news: int = 10):
        """
        获取股票新闻并进行情感分析
        
        通过获取指定股票的最新新闻，使用AI模型分析新闻内容的整体情感倾向，
        为投资决策提供情感面参考。
        
        Args:
            symbol (str): 股票代码/股票符号 (例如: "000001", "600000")
            max_news (int): 获取的新闻条数，默认为10条
        
        Returns:
            str: 情感分析结果
                - "positive": 正面情感，新闻整体偏向利好
                - "negative": 负面情感，新闻整体偏向利空  
                - "neutral": 中性情感，新闻无明显倾向
        
        Example:
            >>> toolkit = Toolkit()
            >>> sentiment = toolkit.get_sentiments("000001", 5)
            >>> print(sentiment)  # "positive" or "negative" or "neutral"
        """
        return get_sentiment(symbol, max_news)

    @staticmethod
    @tool
    def get_news(symbol: str, max_news: int = 10):
        """
        获取指定股票的最新新闻数据
        
        通过爬取东方财富等财经网站，获取指定股票代码的最新新闻信息，
        包括新闻标题、内容、发布时间和链接等详细信息。
        
        Args:
            symbol (str): 股票代码/股票符号 (例如: "000001", "600000")
            max_news (int): 获取的新闻条数，默认为10条，最大支持100条
        
        Returns:
            List[Dict]: 新闻列表，每条新闻包含以下字段：
                - title (str): 新闻标题
                - content (str): 新闻内容
                - publish_time (str): 发布时间
                - url (str): 新闻链接
        
        Example:
            >>> toolkit = Toolkit()
            >>> news_list = toolkit.get_news("000001", 5)
            >>> print(f"获取到 {len(news_list)} 条新闻")
            >>> print(news_list[0]['title'])  # 打印第一条新闻标题
        """
        return get_stock_news(symbol, max_news)

