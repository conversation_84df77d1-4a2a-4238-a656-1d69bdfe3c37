# """
# API工具模块 - 提供Agent共享的API功能组件

# 此模块定义了全局FastAPI应用实例和路由注册机制，
# 为各个Agent提供统一的API暴露方式。

# 注意: 大部分功能已被重构到backend目录，此模块仅为向后兼容性而保留。
# """

# from fastapi import APIRouter
# from backend.main import app
# import json
# import logging
# import functools
# import threading
# import time
# import inspect
# from typing import Dict, List, Any, Optional, Callable, TypeVar
# from datetime import datetime, UTC
# import uvicorn
# import sys
# import io

# # 导入重构后的模块
# from backend.models.api_models import RunInfo
# from backend.state import api_state
# from backend.utils.api_utils import (
#     safe_parse_json,
#     format_llm_request,
#     format_llm_response
# )
# from backend.schemas import LLMInteractionLog, AgentExecutionLog
# from src.utils.serialization import serialize_agent_state

# # 导入日志记录器
# try:
#     from src.utils.llm_interaction_logger import set_global_log_storage
#     from backend.dependencies import get_log_storage
#     _has_log_system = True
# except ImportError:
#     _has_log_system = False
#     def set_global_log_storage(storage):
#         pass
#     def get_log_storage():
#         return None

# # 统一定义 logger
# logger = logging.getLogger("api_utils")

# # 设置全局日志存储器
# if _has_log_system:
#     try:
#         storage = get_log_storage()
#         set_global_log_storage(storage)
#     except Exception as e:
#         logger.warning(f"设置全局日志存储器失败: {str(e)}")

# # 类型定义
# T = TypeVar('T')

# # 全局字典用于跟踪每个agent的LLM调用
# _agent_llm_calls = {}

# # -----------------------------------------------------------------------------
# # FastAPI应用
# # -----------------------------------------------------------------------------

# # 这些路由器不再使用，仅为向后兼容性保留定义
# agents_router = APIRouter(tags=["Agents"])
# runs_router = APIRouter(tags=["Runs"])
# workflow_router = APIRouter(tags=["Workflow"])

# # -----------------------------------------------------------------------------
# # 装饰器和工具函数
# # -----------------------------------------------------------------------------


# def log_llm_interaction(state):
#     """记录LLM交互的装饰器函数"""
#     # 检查是否是直接函数调用模式（向后兼容）
#     if isinstance(state, str) and len(state) > 0:
#         agent_name = state
        
#         def direct_logger(request_data, response_data):
#             formatted_request = format_llm_request(request_data)
#             formatted_response = format_llm_response(response_data)
#             timestamp = datetime.now(UTC)
#             run_id = api_state.current_run_id

#             api_state.update_agent_data(agent_name, "llm_request", formatted_request)
#             api_state.update_agent_data(agent_name, "llm_response", formatted_response)
#             api_state.update_agent_data(agent_name, "llm_timestamp", timestamp.isoformat())

#             # 保存到BaseLogStorage
#             try:
#                 if _has_log_system:
#                     log_storage = get_log_storage()
#                     if log_storage:
#                         log_entry = LLMInteractionLog(
#                             agent_name=agent_name,
#                             run_id=run_id,
#                             request_data=formatted_request,
#                             response_data=formatted_response,
#                             timestamp=timestamp
#                         )
#                         log_storage.add_log(log_entry)
#                         logger.debug(f"已将直接调用的LLM交互保存到日志存储: {agent_name}")
#             except Exception as log_err:
#                 logger.warning(f"保存直接调用的LLM交互到日志存储失败: {str(log_err)}")

#             return response_data
#         return direct_logger

#     # 装饰器工厂模式
#     def decorator(llm_func):
#         @functools.wraps(llm_func)
#         def wrapper(*args, **kwargs):
#             caller_frame = inspect.currentframe().f_back
#             caller_info = {
#                 "function": llm_func.__name__,
#                 "file": caller_frame.f_code.co_filename,
#                 "line": caller_frame.f_lineno
#             }

#             result = llm_func(*args, **kwargs)

#             # 从state中提取agent_name和run_id
#             agent_name = None
#             run_id = None

#             if isinstance(state, dict):
#                 agent_name = state.get("metadata", {}).get("current_agent_name")
#                 run_id = state.get("metadata", {}).get("run_id")

#             # 如果state中没有，尝试从上下文变量中获取
#             if not agent_name:
#                 try:
#                     from src.utils.llm_interaction_logger import current_agent_name_context, current_run_id_context
#                     agent_name = current_agent_name_context.get()
#                     run_id = current_run_id_context.get()
#                 except (ImportError, AttributeError):
#                     pass

#             # 如果仍然没有，尝试从api_state中获取
#             if not agent_name and hasattr(api_state, "current_agent_name"):
#                 agent_name = api_state.current_agent_name
#                 run_id = api_state.current_run_id

#             if agent_name:
#                 timestamp = datetime.now(UTC)

#                 # 提取参数
#                 messages = kwargs.get("messages") or (args[0] if args else None)
#                 model = kwargs.get("model")
#                 client_type = kwargs.get("client_type", "auto")

#                 formatted_request = {
#                     "caller": caller_info,
#                     "messages": messages,
#                     "model": model,
#                     "client_type": client_type,
#                     "arguments": format_llm_request(args),
#                     "kwargs": format_llm_request(kwargs) if kwargs else {}
#                 }

#                 formatted_response = format_llm_response(result)

#                 # 记录到API状态
#                 api_state.update_agent_data(agent_name, "llm_request", formatted_request)
#                 api_state.update_agent_data(agent_name, "llm_response", formatted_response)
#                 api_state.update_agent_data(agent_name, "llm_timestamp", timestamp.isoformat())

#                 # 保存到BaseLogStorage
#                 try:
#                     if _has_log_system:
#                         log_storage = get_log_storage()
#                         if log_storage:
#                             log_entry = LLMInteractionLog(
#                                 agent_name=agent_name,
#                                 run_id=run_id,
#                                 request_data=formatted_request,
#                                 response_data=formatted_response,
#                                 timestamp=timestamp
#                             )
#                             log_storage.add_log(log_entry)
#                             logger.debug(f"已将装饰器捕获的LLM交互保存到日志存储: {agent_name}")
#                 except Exception as log_err:
#                     logger.warning(f"保存装饰器捕获的LLM交互到日志存储失败: {str(log_err)}")

#             return result
#         return wrapper
#     return decorator


# def agent_endpoint(agent_name: str, description: str = ""):
#     """为Agent创建API端点的装饰器"""
#     def decorator(agent_func):
#         # 注册Agent
#         api_state.register_agent(agent_name, description)
#         _agent_llm_calls[agent_name] = False

#         @functools.wraps(agent_func)
#         def wrapper(state):
#             # 更新Agent状态为运行中
#             api_state.update_agent_state(agent_name, "running")

#             # 添加当前agent名称到状态元数据
#             if "metadata" not in state:
#                 state["metadata"] = {}
#             state["metadata"]["current_agent_name"] = agent_name

#             # 确保run_id在元数据中
#             run_id = state.get("metadata", {}).get("run_id")
            
#             # 记录输入状态
#             timestamp_start = datetime.now(UTC)
#             serialized_input = serialize_agent_state(state)
#             api_state.update_agent_data(agent_name, "input_state", serialized_input)

#             # 设置输出重定向
#             old_stdout = sys.stdout
#             old_stderr = sys.stderr
#             redirect_stdout = io.StringIO()
#             redirect_stderr = io.StringIO()
#             log_stream = io.StringIO()
            
#             # 创建日志处理器
#             log_handler = logging.StreamHandler(log_stream)
#             log_handler.setLevel(logging.DEBUG)
#             root_logger = logging.getLogger()
#             root_logger.addHandler(log_handler)
            
#             # 重定向输出
#             sys.stdout = redirect_stdout
#             sys.stderr = redirect_stderr
            
#             terminal_outputs = []
#             reasoning_details = None

#             try:
#                 # 执行Agent核心逻辑
#                 result = agent_func(state)
#                 timestamp_end = datetime.now(UTC)

#                 # 恢复标准输出/错误
#                 sys.stdout = old_stdout
#                 sys.stderr = old_stderr
#                 root_logger.removeHandler(log_handler)

#                 # 获取捕获的输出
#                 stdout_content = redirect_stdout.getvalue()
#                 stderr_content = redirect_stderr.getvalue()
#                 log_content = log_stream.getvalue()
                
#                 if stdout_content:
#                     terminal_outputs.append(stdout_content)
#                 if stderr_content:
#                     terminal_outputs.append(stderr_content)
#                 if log_content:
#                     terminal_outputs.append(log_content)

#                 # 序列化输出状态
#                 serialized_output = serialize_agent_state(result)
#                 api_state.update_agent_data(agent_name, "output_state", serialized_output)

#                 # 更新Agent状态为已完成
#                 api_state.update_agent_state(agent_name, "completed")

#                 # 添加Agent执行日志到BaseLogStorage
#                 try:
#                     if _has_log_system:
#                         log_storage = get_log_storage()
#                         if log_storage:
#                             log_entry = AgentExecutionLog(
#                                 agent_name=agent_name,
#                                 run_id=run_id,
#                                 timestamp_start=timestamp_start,
#                                 timestamp_end=timestamp_end,
#                                 input_state=serialized_input,
#                                 output_state=serialized_output,
#                                 reasoning_details=reasoning_details,
#                                 terminal_outputs=terminal_outputs
#                             )
#                             log_storage.add_agent_log(log_entry)
#                             logger.debug(f"已将Agent执行日志保存到存储: {agent_name}, run_id: {run_id}")
#                 except Exception as log_err:
#                     logger.error(f"保存Agent执行日志到存储失败: {agent_name}, {str(log_err)}")

#                 return result
                
#             except Exception as e:
#                 timestamp_end = datetime.now(UTC)
#                 error = str(e)
                
#                 # 恢复标准输出/错误
#                 sys.stdout = old_stdout
#                 sys.stderr = old_stderr
#                 root_logger.removeHandler(log_handler)
                
#                 # 获取捕获的输出
#                 stdout_content = redirect_stdout.getvalue()
#                 stderr_content = redirect_stderr.getvalue()
#                 log_content = log_stream.getvalue()
                
#                 if stdout_content:
#                     terminal_outputs.append(stdout_content)
#                 if stderr_content:
#                     terminal_outputs.append(stderr_content)
#                 if log_content:
#                     terminal_outputs.append(log_content)

#                 # 更新Agent状态为错误
#                 api_state.update_agent_state(agent_name, "error")
#                 api_state.update_agent_data(agent_name, "error", error)

#                 # 添加错误日志到BaseLogStorage
#                 try:
#                     if _has_log_system:
#                         log_storage = get_log_storage()
#                         if log_storage:
#                             log_entry = AgentExecutionLog(
#                                 agent_name=agent_name,
#                                 run_id=run_id,
#                                 timestamp_start=timestamp_start,
#                                 timestamp_end=timestamp_end,
#                                 input_state=serialized_input,
#                                 output_state={"error": error},
#                                 reasoning_details=None,
#                                 terminal_outputs=terminal_outputs
#                             )
#                             log_storage.add_agent_log(log_entry)
#                             logger.debug(f"已将Agent错误日志保存到存储: {agent_name}, run_id: {run_id}")
#                 except Exception as log_err:
#                     logger.error(f"保存Agent错误日志到存储失败: {agent_name}, {str(log_err)}")

#                 raise

#         return wrapper
#     return decorator


# # 启动API服务器的函数
# def start_api_server(host="0.0.0.0", port=8000, stop_event=None):
#     """在独立线程中启动API服务器"""
#     if stop_event:
#         # 使用支持优雅关闭的配置
#         config = uvicorn.Config(
#             app=app,
#             host=host,
#             port=port,
#             log_config=None,
#             # 开启ctrl+c处理
#             use_colors=True
#         )
#         server = uvicorn.Server(config)

#         # 运行服务器并在单独线程中监听stop_event
#         def check_stop_event():
#             # 在后台检查stop_event
#             while not stop_event.is_set():
#                 time.sleep(0.5)
#             # 当stop_event被设置时，请求服务器退出
#             logger.info("收到停止信号，正在关闭API服务器...")
#             server.should_exit = True

#         # 启动stop_event监听线程
#         stop_monitor = threading.Thread(
#             target=check_stop_event,
#             daemon=True
#         )
#         stop_monitor.start()

#         # 运行服务器（阻塞调用，但会响应should_exit标志）
#         try:
#             server.run()
#         except KeyboardInterrupt:
#             # 如果还是收到了KeyboardInterrupt，确保我们的stop_event也被设置
#             stop_event.set()
#         logger.info("API服务器已关闭")
#     else:
#         # 默认方式启动，不支持外部停止控制但仍响应Ctrl+C
#         uvicorn.run(app, host=host, port=port, log_config=None)
