import os
import sys
import json
from datetime import datetime
import akshare as ak
import requests
from bs4 import BeautifulSoup
import time
import pandas as pd
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder

def get_stock_news(symbol: str, max_news: int = 10) -> list:
    """获取并处理个股新闻

    Args:
        symbol (str): 股票代码，如 "000001"
        max_news (int, optional): 获取的新闻条数，默认为10条。最大支持100条。

    Returns:
        list: 新闻列表，每条新闻包含title、content、publish_time、url等信息
    """
    # 限制最大新闻条数
    max_news = min(max_news, 10)
    
    # 获取当前日期
    today = datetime.now().strftime("%Y-%m-%d")
    
    # 构建新闻文件路径
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    news_dir = os.path.join(project_root, "datasets", "stock_news")
    os.makedirs(news_dir, exist_ok=True)
    
    news_file = os.path.join(news_dir, f"{symbol}_news.json")
    print(f"新闻文件路径: {news_file}")
    
    # 检查缓存
    if os.path.exists(news_file):
        try:
            with open(news_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if data.get("date") == today and len(data.get("news", [])) >= max_news:
                    print(f"使用缓存的新闻数据")
                    return data["news"][:max_news]
        except Exception as e:
            print(f"读取缓存失败: {e}")
    
    print(f'开始获取{symbol}的新闻数据...')
    
    try:
        # 获取新闻数据
        news_df = ak.stock_news_em(symbol=symbol)
        if news_df is None or len(news_df) == 0:
            print(f"未获取到{symbol}的新闻数据")
            return []
        
        print(f"成功获取到{len(news_df)}条原始新闻")
        
        # 处理新闻数据
        news_list = []
        for _, row in news_df.head(max_news * 2).iterrows():  # 多获取一些以防内容为空
            try:
                # 获取新闻内容，优先使用新闻内容，否则使用标题
                content = row.get("新闻内容", "")
                if pd.isna(content) or not str(content).strip():
                    content = row.get("新闻标题", "")
                
                content = str(content).strip()
                if len(content) < 10:  # 跳过内容太短的新闻
                    continue
                
                # 构建新闻项
                news_item = {
                    "title": str(row.get("新闻标题", "")).strip(),
                    "content": content,
                    "publish_time": str(row.get("发布时间", "")),
                    "url": str(row.get("新闻链接", "")).strip()
                }
                
                # 验证必要字段
                if news_item["title"] and news_item["content"]:
                    news_list.append(news_item)
                    print(f"成功添加新闻: {news_item['title'][:50]}...")
                
                if len(news_list) >= max_news:
                    break
                    
            except Exception as e:
                print(f"处理单条新闻时出错: {e}")
                continue
        
        # 按发布时间排序
        news_list.sort(key=lambda x: x["publish_time"], reverse=True)
        
        # 保存到文件
        save_data = {"date": today, "news": news_list}
        with open(news_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"成功保存{len(news_list)}条新闻到文件")
        return news_list
        
    except Exception as e:
        print(f"获取新闻数据时出错: {e}")
        return []






def get_sentiment(symbol: str, max_news: int = 10) -> str:
    """获取股票新闻并进行情感分析
    
    Args:
        symbol (str): 股票代码，如 "000001"
        max_news (int, optional): 获取的新闻条数，默认为10条
        
    Returns:
        str: 情感分析结果，"positive" 或 "negative"
    """
    # 导入配置和LLM
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from trading.default_config import DEFAULT_CONFIG
    from langchain_openai import ChatOpenAI
    
    # 获取新闻数据
    news_list = get_stock_news(symbol, max_news)
    if not news_list:
        print("未获取到新闻数据，无法进行情感分析")
        return "neutral"
    
    # 构建分析文本
    news_text = ""
    for news in news_list:
        news_text += f"标题: {news['title']}\n内容: {news['content'][:200]}...\n\n"
    
    # 初始化LLM
    llm = ChatOpenAI(
        model=DEFAULT_CONFIG["sentiment_llm"],
        base_url=DEFAULT_CONFIG["sentiment_url"],
        api_key=DEFAULT_CONFIG["sentiment_api_key"]
    )
    
    # 创建提示模板
    prompt = ChatPromptTemplate.from_messages([
        ("system", "你是一个专业的金融情感分析师。请分析以下股票新闻的整体情感倾向，只回答'positive'或'negative'或'neutral'，不要其他解释。"),
        ("user", "请分析股票{symbol}的以下新闻情感:\n{news_text}")
    ])
    
    # 创建链
    chain = prompt | llm
    
    try:
        # 调用链进行情感分析
        result = chain.invoke({
            "symbol": symbol,
            "news_text": news_text
        })
        
        sentiment = result.content.strip().lower()
        
        if "positive" in sentiment:
            return "positive"
        elif "negative" in sentiment:
            return "negative"
        else:
            return "neutral"
            
    except Exception as e:
        print(f"情感分析失败: {e}")
        return "neutral"






if __name__ == "__main__":
    # result = get_stock_news("000001",10)
    # print(f"获取到 {len(result)} 条新闻")
    # for i, news in enumerate(result, 1):
    #     print(f"{i}. {news['title']}")
    result = get_sentiment("000001",10)
    print(f"情感分析结果: {result}")    
