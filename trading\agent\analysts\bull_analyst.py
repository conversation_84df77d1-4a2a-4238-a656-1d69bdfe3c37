from langchain_core.messages import AIMessage
import time
import json


def create_bull_researcher(llm, memory):
    def bull_node(state) -> dict:
        investment_debate_state = state["investment_debate_state"]
        history = investment_debate_state.get("history", "")
        bull_history = investment_debate_state.get("bull_history", "")
        current_response = investment_debate_state.get("current_response", "")
        
        sentiment_report = state["sentiment_report"]
        fundamentals_report = state["fundamentals_report"]

        curr_situation = f"{sentiment_report}\n\n{fundamentals_report}"
        past_memories = memory.get_memories(curr_situation, n_matches=2)

        past_memory_str = ""
        for i, rec in enumerate(past_memories, 1):
            past_memory_str += rec["recommendation"] + "\n\n"

        prompt = f"""你是一名多头分析师，负责论证应该投资该股票。你的任务是构建强有力的、基于证据的论证，强调增长潜力、竞争优势和积极的市场指标。利用提供的研究和数据来解决担忧并有效反驳空头论点。

重点关注以下方面：
- 增长潜力：突出公司的市场机会、收入预测和可扩展性。
- 竞争优势：强调独特产品、强势品牌或主导市场地位等因素。
- 积极指标：使用财务健康状况、行业趋势和近期积极新闻作为证据。
- 反驳空头观点：用具体数据和合理推理批判性地分析空头论点，全面解决担忧并展示为什么多头观点更有说服力。
- 互动辩论：以对话风格呈现你的论点，直接回应空头分析师的观点并进行有效辩论，而不是简单地罗列数据。

可用资源：
社交媒体情感报告：{sentiment_report}
公司基本面报告：{fundamentals_report}
辩论历史记录：{history}
最新空头论点：{current_response}
类似情况的反思和经验教训：{past_memory_str}
使用这些信息提出令人信服的多头论证，反驳空头的担忧，并参与动态辩论，展示多头立场的优势。你还必须结合反思，从过去的经验教训和错误中学习。
"""

        response = llm.invoke(prompt)

        argument = f"Bull Analyst: {response.content}"

        new_investment_debate_state = {
            "history": history + "\n" + argument,
            "bull_history": bull_history + "\n" + argument,
            "bear_history": investment_debate_state.get("bear_history", ""),
            "current_response": argument,
            "count": investment_debate_state["count"] + 1,
        }

        return {"investment_debate_state": new_investment_debate_state}

    return bull_node
