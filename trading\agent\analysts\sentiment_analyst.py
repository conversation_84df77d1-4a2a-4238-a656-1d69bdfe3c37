from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json


def create_news_analyst(llm, toolkit):
    def news_analyst_node(state):
        current_date = state["trade_date"]
        ticker = state["company_name"]
        symbol = state["symbol"]

        tools = [toolkit.get_sentiments,toolkit.get_news]

        system_message = (
            "你是一名新闻研究员，负责分析过去一周的最新新闻和趋势。请撰写一份关于当前世界状况的综合报告，"
            "重点关注与交易和宏观经济相关的内容。查看新闻以确保全面性。"
            "不要简单地说趋势是混合的，请提供详细和细致的分析和见解，帮助交易者做出决策。"
            "确保在报告末尾附上一个Markdown表格来组织报告中的关键点，使其有条理且易于阅读。"
        )

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "你是一个有用的AI助手，与其他助手协作。"
                    "使用提供的工具来回答问题并取得进展。"
                    "如果你无法完全回答，没关系；其他具有不同工具的助手会在你停下的地方继续帮助。"
                    "尽你所能取得进展。"
                    "如果你或任何其他助手有最终交易建议：**买入/持有/卖出**或可交付成果，"
                    "请在回复前加上'最终交易建议：**买入/持有/卖出**'，这样团队就知道要停止了。"
                    "你可以使用以下工具：{tool_names}。\n{system_message}"
                    "供参考，当前日期是{current_date}。我们正在查看公司{ticker}，股票代码{symbol}",
                ),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        prompt = prompt.partial(system_message=system_message)
        prompt = prompt.partial(symbol=symbol)
        prompt = prompt.partial(tool_names=", ".join([tool.name for tool in tools]))
        prompt = prompt.partial(current_date=current_date)
        prompt = prompt.partial(ticker=ticker)

        chain = prompt | llm.bind_tools(tools)
        result = chain.invoke(state["messages"])

        report = ""

        if len(result.tool_calls) == 0:
            report = result.content

        return {
            "messages": [result],
            "sentiment_report": report,
        }

    return news_analyst_node



