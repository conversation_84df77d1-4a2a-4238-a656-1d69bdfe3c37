import json
import hashlib
from langchain_chroma import Chroma
from langchain_community.embeddings import FakeEmbeddings  # 用于示例，实际用CustomEmbeddings
from openai_embedding import CustomEmbeddings
import os

# 计算路径hash前8位
json_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../datasets/finance_instruct_500k.json'))
hash_part = hashlib.md5(json_path.encode('utf-8')).hexdigest()[:8]
collection_name = f"chroma_{hash_part}"

# 初始化自定义embedding
embeddings = CustomEmbeddings(
    api_url="https://api.siliconflow.cn/v1/embeddings",  # 替换为实际API URL
    api_key="sk-yasxyjkrvevjwtbvxsxyaulmxwtibwqoidvjfposfnvvuhhk",  # 替换为实际API Key
    model_id="BAAI/bge-m3",  # 替换为实际model_id
    batch_size=8
)

# 初始化Chroma
persist_directory = os.path.abspath(os.path.join(os.path.dirname(__file__), "../chroma_db"))
vectorstore = Chroma(
    collection_name=collection_name,
    embedding_function=embeddings,
    persist_directory=persist_directory
)

def process_large_json(json_path, vectorstore, batch_size=100):
    """
    逐条处理大json文件，将user做embedding，assistant存入metadata
    """
    with open(json_path, 'r', encoding='utf-8') as f:
        # 读取整个list（假设文件为[{}, {}, ...]）
        data = json.load(f)
        texts = []
        metadatas = []
        ids = []
        for idx, item in enumerate(data):
            user_text = item.get('user', None)
            assistant_text = item.get('assistant', None)
            if user_text and assistant_text:
                texts.append(user_text)
                metadatas.append({"assistant": assistant_text})
                ids.append(str(idx))
            # 批量写入
            if len(texts) >= batch_size:
                vectorstore.add_texts(texts, metadatas=metadatas, ids=ids)
                texts, metadatas, ids = [], [], []
        # 处理最后一批
        if texts:
            vectorstore.add_texts(texts, metadatas=metadatas, ids=ids)
    print(f"数据已全部写入Chroma，collection名: {collection_name}")

if __name__ == "__main__":
    process_large_json(json_path, vectorstore, batch_size=10)
