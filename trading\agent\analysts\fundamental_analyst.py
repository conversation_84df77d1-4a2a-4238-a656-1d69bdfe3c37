from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json

def create_fundamentals_analyst(llm, toolkit):
    def fundamentals_analyst_node(state):
        current_date = state["trade_date"]
        ticker = state["company_name"]
        symbol = state["symbol"]

        # 根据配置选择工具
        # if toolkit.config["online_tools"]:
        #     tools = [toolkit.get_fundamentals_openai]
        # else:
        #     tools = [toolkit.get_fundamentals]  # 使用离线工具
        tools = [toolkit.get_fundamentals]

        system_message = (
            "你是一名研究员，负责分析公司过去一周的基本面信息。"
            "请撰写一份关于公司基本面信息的综合报告，包括财务文件、"
            "公司概况、基本财务数据、公司财务历史、内幕情绪和内幕交易等，"
            "以全面了解公司的基本面信息，为交易员提供参考。"
            "确保包含尽可能多的细节。不要简单地说趋势复杂，"
            "要提供详细和细致的分析和见解，帮助交易员做出决策。"
            "确保在报告末尾附上一个Markdown表格来组织报告中的关键点，使其条理清晰、易于阅读。"
        )

        prompt = ChatPromptTemplate.from_messages([
            (
                "system",
                "你是一个有用的AI助手，与其他助手协作。"
                "使用提供的工具来回答问题并取得进展。"
                "如果你无法完全回答，没关系；另一个拥有不同工具的助手"
                "会从你停下的地方继续。尽你所能取得进展。"
                "如果你或任何其他助手有最终交易建议：**买入/持有/卖出**或可交付成果，"
                "请在回复前加上'最终交易建议：**买入/持有/卖出**'，这样团队就知道可以停止了。"
                "你可以使用以下工具：{tool_names}。\n{system_message} "
                "供参考，当前日期是{current_date}。我们要分析的公司是{ticker}，股票代码：{symbol}"
            ),
            MessagesPlaceholder(variable_name="messages"),
        ])

        prompt = prompt.partial(system_message=system_message)
        prompt = prompt.partial(symbol=symbol)
        prompt = prompt.partial(tool_names=", ".join([tool.name for tool in tools]))
        prompt = prompt.partial(current_date=current_date)
        prompt = prompt.partial(ticker=ticker)

        chain = prompt | llm.bind_tools(tools)

        try:
            result = chain.invoke(state["messages"])
            report = ""

            # 如果没有工具调用，直接使用内容作为报告
            if len(result.tool_calls) == 0:
                report = result.content

            return {
                "messages": [result],
                "fundamentals_report": report,
            }
        except Exception as e:
            # 错误处理
            error_message = f"Error in fundamentals analyst: {str(e)}"
            return {
                "messages": [{"role": "assistant", "content": error_message}],
                "fundamentals_report": error_message,
            }

    return fundamentals_analyst_node
