import pandas as pd
import os
import json
from datetime import datetime, timedelta
import akshare as ak
import numpy as np
from scipy import stats
from typing import List, Dict, Union, Optional
from pathlib import Path

def get_knowledgebase():
    """
    从Hugging Face获取金融指令数据集并保存为JSON格式
    返回: pandas.DataFrame - 加载的数据框
    """
    # Login using e.g. `huggingface-cli login` to access this dataset
    df = pd.read_json("hf://datasets/Josephgflowers/Finance-Instruct-500k/train.json", lines=True)

    # 保存数据到 datasets 文件夹
    output_path = os.path.join("..", "datasets", "finance_instruct_500k.json")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    # 保存为JSON格式，保持数据结构和编码
    df.to_json(output_path, orient='records', force_ascii=False, indent=2)

    print(f"数据已保存到: {output_path}")
    print(f"保存了 {len(df)} 条记录")
    print(f"数据列名: {list(df.columns)}")

    # 显示前三条记录
    print("\n前三条记录:")
    print(df.head(3))
    
    return df

def get_stock_news(symbol: str, max_news: int = 10) -> list:
    """获取并处理个股新闻

    Args:
        symbol (str): 股票代码，如 "300059"
        max_news (int, optional): 获取的新闻条数，默认为10条。最大支持100条。

    Returns:
        list: 新闻列表，每条新闻包含标题、内容、发布时间等信息
    """

    # 设置pandas显示选项，确保显示完整内容
    pd.set_option('display.max_columns', None)
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_colwidth', None)
    pd.set_option('display.width', None)

    # 限制最大新闻条数
    max_news = min(max_news, 100)

    # 获取当前日期
    today = datetime.now().strftime("%Y-%m-%d")

    # 构建新闻文件路径
    # project_root = os.path.dirname(os.path.dirname(
    #     os.path.dirname(os.path.abspath(__file__))))
    news_dir = os.path.join("..", "datasets", "stock_news")
    print(f"新闻保存目录: {news_dir}")

    # 确保目录存在
    try:
        os.makedirs(news_dir, exist_ok=True)
        print(f"成功创建或确认目录存在: {news_dir}")
    except Exception as e:
        print(f"创建目录失败: {e}")
        return []

    news_file = os.path.join(news_dir, f"{symbol}_news.json")
    print(f"新闻文件路径: {news_file}")

    # 检查是否需要更新新闻
    need_update = True
    if os.path.exists(news_file):
        try:
            with open(news_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if data.get("date") == today:
                    cached_news = data.get("news", [])
                    if len(cached_news) >= max_news:
                        print(f"使用缓存的新闻数据: {news_file}")
                        return cached_news[:max_news]
                    else:
                        print(
                            f"缓存的新闻数量({len(cached_news)})不足，需要获取更多新闻({max_news}条)")
        except Exception as e:
            print(f"读取缓存文件失败: {e}")

    print(f'开始获取{symbol}的新闻数据...')

    try:
        # 获取新闻列表
        news_df = ak.stock_news_em(symbol=symbol)
        if news_df is None or len(news_df) == 0:
            print(f"未获取到{symbol}的新闻数据")
            return []

        print(f"成功获取到{len(news_df)}条新闻")

        # 实际可获取的新闻数量
        available_news_count = len(news_df)
        if available_news_count < max_news:
            print(f"警告：实际可获取的新闻数量({available_news_count})少于请求的数量({max_news})")
            max_news = available_news_count

        # 获取指定条数的新闻（考虑到可能有些新闻内容为空，多获取50%）
        news_list = []
        for _, row in news_df.head(int(max_news * 1.5)).iterrows():
            try:
                # 获取新闻内容
                content = row["新闻内容"] if "新闻内容" in row and not pd.isna(
                    row["新闻内容"]) else ""
                if not content:
                    content = row["新闻标题"]

                # 只去除首尾空白字符
                content = content.strip()
                if len(content) < 10:  # 内容太短的跳过
                    continue

                # 获取关键词
                keyword = row["关键词"] if "关键词" in row and not pd.isna(
                    row["关键词"]) else ""

                # 添加新闻
                news_item = {
                    "title": row["新闻标题"].strip(),
                    "content": content,
                    "publish_time": row["发布时间"],
                    "source": row["文章来源"].strip(),
                    "url": row["新闻链接"].strip(),
                    "keyword": keyword.strip()
                }
                news_list.append(news_item)
                print(f"成功添加新闻: {news_item['title']}")

            except Exception as e:
                print(f"处理单条新闻时出错: {e}")
                continue

        # 按发布时间排序
        news_list.sort(key=lambda x: x["publish_time"], reverse=True)

        # 只保留指定条数的有效新闻
        news_list = news_list[:max_news]

        # 保存到文件
        try:
            save_data = {
                "date": today,
                "news": news_list
            }
            with open(news_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            print(f"成功保存{len(news_list)}条新闻到文件: {news_file}")
        except Exception as e:
            print(f"保存新闻数据到文件时出错: {e}")

        return news_list

    except Exception as e:
        print(f"获取新闻数据时出错: {e}")
        return []

def get_price(symbol: str, start_date: str = None, end_date: str = None, adjust: str = "qfq") -> dict:
    """
    获取股票价格数据并计算技术指标
    
    Args:
        symbol (str): 股票代码，如 "000001"
        start_date (str): 开始日期，格式 "YYYY-MM-DD"，默认为一年前
        end_date (str): 结束日期，格式 "YYYY-MM-DD"，默认为昨天
        adjust (str): 复权类型，可选值：
               - "": 不复权
               - "qfq": 前复权（默认）
               - "hfq": 后复权
        
    Returns:
        dict: 包含价格数据和技术指标的字典
    """
    try:
        # 获取当前日期和昨天的日期
        current_date = datetime.now()
        yesterday = current_date - timedelta(days=1)

        # 如果没有提供日期，默认使用昨天作为结束日期
        if not end_date:
            end_date = yesterday
        else:
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
            # 确保end_date不会超过昨天
            if end_date > yesterday:
                end_date = yesterday

        if not start_date:
            start_date = end_date - timedelta(days=365)  # 默认获取一年的数据
        else:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")

        print(f"\n获取股票 {symbol} 的历史价格数据...")
        print(f"开始日期: {start_date.strftime('%Y-%m-%d')}")
        print(f"结束日期: {end_date.strftime('%Y-%m-%d')}")

        def get_and_process_data(start_date, end_date):
            """获取并处理数据，包括重命名列等操作"""
            df = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date.strftime("%Y%m%d"),
                end_date=end_date.strftime("%Y%m%d"),
                adjust=adjust
            )

            if df is None or df.empty:
                return pd.DataFrame()

            # 重命名列以匹配标准格式
            df = df.rename(columns={
                "日期": "date",
                "开盘": "open",
                "最高": "high",
                "最低": "low",
                "收盘": "close",
                "成交量": "volume",
                "成交额": "amount",
                "振幅": "amplitude",
                "涨跌幅": "pct_change",
                "涨跌额": "change_amount",
                "换手率": "turnover"
            })

            # 确保日期列为datetime类型
            df["date"] = pd.to_datetime(df["date"])
            
            # 确保数值列为数值类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'amplitude', 'pct_change', 'change_amount', 'turnover']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 删除多余的列（如"股票代码"）
            columns_to_keep = ['date', 'open', 'high', 'low', 'close', 'volume', 'amount', 'amplitude', 'pct_change', 'change_amount', 'turnover']
            df = df[columns_to_keep]
            
            return df

        # 获取历史行情数据
        df = get_and_process_data(start_date, end_date)

        if df is None or df.empty:
            print(f"警告: 未找到股票 {symbol} 的历史价格数据")
            return {}

        # 检查数据量是否足够
        min_required_days = 120  # 至少需要120个交易日的数据
        if len(df) < min_required_days:
            print(f"警告: 数据量不足 ({len(df)} 天)，无法计算完整的技术指标")
            print("尝试获取更多数据...")

            # 扩大时间范围到2年
            start_date = end_date - timedelta(days=730)
            df = get_and_process_data(start_date, end_date)

            if len(df) < min_required_days:
                print(f"警告: 即使扩大时间范围，数据量仍然不足 ({len(df)} 天)")

        # 计算动量指标
        df["momentum_1m"] = df["close"].pct_change(periods=20) * 100  # 20个交易日约等于1个月
        df["momentum_3m"] = df["close"].pct_change(periods=60) * 100  # 60个交易日约等于3个月
        df["momentum_6m"] = df["close"].pct_change(periods=120) * 100  # 120个交易日约等于6个月

        # 计算成交量动量（相对于20日平均成交量的变化）
        df["volume_ma20"] = df["volume"].rolling(window=20, min_periods=1).mean()
        df["volume_momentum"] = (df["volume"] / df["volume_ma20"] - 1) * 100

        # 计算波动率指标
        # 1. 历史波动率 (20日)
        returns = df["close"].pct_change()
        df["historical_volatility"] = returns.rolling(window=20, min_periods=10).std() * np.sqrt(252) * 100  # 年化

        # 2. 波动率区间 (相对于过去120天的波动率的位置)
        volatility_120d = returns.rolling(window=120, min_periods=60).std() * np.sqrt(252)
        vol_min = volatility_120d.rolling(window=120, min_periods=60).min()
        vol_max = volatility_120d.rolling(window=120, min_periods=60).max()
        vol_range = vol_max - vol_min
        df["volatility_regime"] = np.where(
            vol_range > 0,
            (df["historical_volatility"] - vol_min) / vol_range,
            0  # 当范围为0时返回0
        )

        # 3. 波动率Z分数
        vol_mean = df["historical_volatility"].rolling(window=120, min_periods=60).mean()
        vol_std = df["historical_volatility"].rolling(window=120, min_periods=60).std()
        df["volatility_z_score"] = np.where(
            vol_std > 0,
            (df["historical_volatility"] - vol_mean) / vol_std,
            np.nan
        )

        # 4. ATR比率
        tr = pd.DataFrame()
        tr["h-l"] = df["high"] - df["low"]
        tr["h-pc"] = abs(df["high"] - df["close"].shift(1))
        tr["l-pc"] = abs(df["low"] - df["close"].shift(1))
        tr["tr"] = tr[["h-l", "h-pc", "l-pc"]].max(axis=1)
        df["atr"] = tr["tr"].rolling(window=14, min_periods=7).mean()
        df["atr_ratio"] = (df["atr"] / df["close"]) * 100

        # 计算统计套利指标
        # 1. 赫斯特指数 (使用过去120天的数据)
        def calculate_hurst(series):
            """计算Hurst指数"""
            try:
                series = series.dropna()
                if len(series) < 30:
                    return np.nan

                # 使用对数收益率
                log_returns = np.log(series / series.shift(1)).dropna()
                if len(log_returns) < 30:
                    return np.nan

                # 使用更小的lag范围
                lags = range(2, min(11, len(log_returns) // 4))

                # 计算每个lag的标准差
                tau = []
                for lag in lags:
                    std = log_returns.rolling(window=lag).std().dropna()
                    if len(std) > 0:
                        tau.append(np.mean(std))

                if len(tau) < 3:
                    return np.nan

                # 使用对数回归
                lags_log = np.log(list(lags))
                tau_log = np.log(tau)

                # 计算回归系数
                reg = np.polyfit(lags_log, tau_log, 1)
                hurst = reg[0] / 2.0

                if np.isnan(hurst) or np.isinf(hurst):
                    return np.nan

                return hurst

            except Exception as e:
                return np.nan

        # 使用对数收益率计算Hurst指数
        log_returns = np.log(df["close"] / df["close"].shift(1))
        df["hurst_exponent"] = log_returns.rolling(
            window=120,
            min_periods=60
        ).apply(calculate_hurst)

        # 2. 偏度 (20日)
        df["skewness"] = returns.rolling(window=20, min_periods=10).skew()

        # 3. 峰度 (20日)
        df["kurtosis"] = returns.rolling(window=20, min_periods=10).kurt()

        # 按日期升序排序
        df = df.sort_values("date")

        # 重置索引
        df = df.reset_index(drop=True)

        print(f"成功获取价格历史数据 ({len(df)} 条记录)")

        # 检查并报告NaN值
        nan_columns = df.isna().sum()
        if nan_columns.any():
            print("\n警告: 以下指标包含NaN值:")
            for col, nan_count in nan_columns[nan_columns > 0].items():
                print(f"- {col}: {nan_count} 条记录")

        # 转换为字典格式
        result = {
            "symbol": symbol,
            "adjust": adjust,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "data_count": len(df),
            "data": df.to_dict('records')
        }
        
        # 保存到文件
        save_price_data(symbol, result)
        
        return result

    except Exception as e:
        print(f"获取股票数据时出错: {e}")
        return {}

def save_price_data(symbol: str, data: dict):
    """保存价格数据到JSON文件"""
    try:
        # 创建保存目录
        price_dir = os.path.join("..", "datasets", "stock_prices")
        os.makedirs(price_dir, exist_ok=True)
        
        # 保存文件路径
        price_file = os.path.join(price_dir, f"{symbol}_price_data.json")
        
        # 保存数据
        with open(price_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"价格数据已保存到: {price_file}")
        
    except Exception as e:
        print(f"保存价格数据时出错: {e}")

def get_realtime_quotes(stock_list: Optional[List[str]] = None, save: bool = True) -> pd.DataFrame:
    """
    获取A股实时行情数据
    
    参数:
        stock_list: 可选，股票代码列表，如 ['sh600000', 'sz000001']
                    如果为None，则获取所有A股的实时行情
        save: 是否保存数据到CSV文件
        
    返回:
        pandas.DataFrame: 包含股票行情的数据框
    """
    try:
        # 获取A股实时行情
        stock_spot_df = ak.stock_zh_a_spot_em()
        
        def standardize_symbol(symbol: str) -> str:
            """
            标准化股票代码，添加交易所前缀
    
            参数:
            symbol: 股票代码，如 '600000' 或 'sh600000'
    
            返回:
                str: 标准化后的股票代码，如 'sh600000'
            """
            if not symbol.startswith(('sh', 'sz')):
                if symbol.startswith(('6', '9')):  # 上交所
                    return f'sh{symbol}'
                elif symbol.startswith(('0', '3')):  # 深交所
                    return f'sz{symbol}'
            return symbol

        def standardize_symbol_list(symbols: List[str]) -> List[str]:
            """
            标准化股票代码列表
    
            参数:
            symbols: 股票代码列表，如 ['600000', '000001']
    
            返回:
                List[str]: 标准化后的股票代码列表，如 ['sh600000', 'sz000001']
            """
            return [standardize_symbol(symbol) for symbol in symbols]
        
        
        
        if stock_list:
            # 标准化股票代码列表
            standardized_stocks = standardize_symbol_list(stock_list)
            # 如果提供了股票列表，则筛选相应的股票
            stock_spot_df = stock_spot_df[stock_spot_df['代码'].isin([code[2:] for code in standardized_stocks])]
        
        
        save_path = os.path.join("..","datasets", "stock_realtime_prices")
        os.makedirs(save_path, exist_ok=True)
        if save_path:
            save_dir = Path(save_path).parent
            if not save_dir.exists():
                save_dir.mkdir(parents=True)
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            file_path = os.path.join(save_path, f"stock_quotes_{timestamp}.csv")
            stock_spot_df.to_csv(file_path, index=False, encoding='utf-8-sig')
            print(f"数据已保存到: {file_path}")
        
        return stock_spot_df
    except Exception as e:
        print(f"获取股票行情失败: {e}")
        return pd.DataFrame()

    """
    获取单只股票的详细信息
    
    参数:
        stock_code: 股票代码，如 'sh600000'
        save: 是否保存数据到CSV文件
    
    返回:
        dict: 包含股票详细信息的字典
    """
    if self.stock_data is None or self.stock_data.empty:
        self.get_realtime_quotes(save=False)
        
    # 从数据框中筛选股票信息
    stock_info = self.stock_data[self.stock_data['代码'] == stock_code[2:]]
    
    if not stock_info.empty:
        stock_dict = stock_info.iloc[0].to_dict()
        
        save_path = os.path.join("datasets", "stock_realtime_top")
        
        if save_path:
            save_dir = Path(save_path).parent
            if not save_dir.exists():
                save_dir.mkdir(parents=True)
            # 生成带股票代码的文件名
            file_path = os.path.join(save_path, f"stock_info_{stock_code}.csv")
            pd.DataFrame([stock_dict]).to_csv(file_path, index=False, encoding='utf-8-sig')
            print(f"股票 {stock_code} 信息已保存到: {file_path}")
        
        return stock_dict
    else:
        print(f"未找到股票 {stock_code} 的信息")
        return {}

def get_top_stocks(stock_data: pd.DataFrame, n: int = 10, sort_by: str = '涨跌幅', ascending: bool = False, save: bool = True) -> pd.DataFrame:
    """
    获取涨幅/跌幅排行靠前的股票
    
    参数:
        stock_data: 股票行情数据的DataFrame
        n: 显示的股票数量
        sort_by: 排序依据的列名，如 '涨跌幅', '成交量', '成交额' 等
        ascending: 是否升序排列，默认为False(降序)
        save: 是否保存数据到CSV文件
    
    返回:
        pandas.DataFrame: 排序后的股票数据框
    """
    if stock_data is None or stock_data.empty:
        print("股票数据为空，无法排序")
        return pd.DataFrame()
    try:
        top_stocks = stock_data.sort_values(by=sort_by, ascending=ascending).head(n)
        if save:
            # 生成带排序依据的文件名
            order = "asc" if ascending else "desc"
            save_path = os.path.join("..","datasets", "stock_realtime_top")
            os.makedirs(save_path, exist_ok=True)
            file_path = os.path.join(save_path, f"top_{n}_{sort_by}_{order}.csv")
            top_stocks.to_csv(file_path, index=False, encoding='utf-8-sig')
            print(f"排行数据已保存到: {file_path}")
        return top_stocks
    except KeyError:
        print(f"排序依据的列名 {sort_by} 不存在")
        return pd.DataFrame()

def standardize_symbol(symbol: str) -> str:
            """
            标准化股票代码，添加交易所前缀
    
            参数:
            symbol: 股票代码，如 '600000' 或 'sh600000'
    
            返回:
                str: 标准化后的股票代码，如 'sh600000'
            """
            if not symbol.startswith(('sh', 'sz')):
                if symbol.startswith(('6', '9')):  # 上交所
                    return f'SH{symbol}'
                elif symbol.startswith(('0', '3')):  # 深交所
                    return f'SZ{symbol}'
            return symbol

def get_balance_sheet(stock_code: str = "00020", market: str = "HK", period: str = "年度", verbose: bool = False) -> Optional[pd.DataFrame]:
    """
    获取公司的资产负债表
    
    Args:
        stock_code (str): 股票代码
        market (str): 股票市场，"HK"为港股，"A"为A股
        period (str): 报告期间，可选"年度"或"中期"（仅港股适用），默认为"年度"
        verbose (bool): 是否打印详细信息，默认为False
    
    Returns:
        pd.DataFrame: 资产负债表数据，如果获取失败则返回None
    """
    try:
        if verbose:
            print(f"正在获取{market}股票代码 {stock_code} 的{period}资产负债表...")
        
        
        stock_code = standardize_symbol(stock_code)
        
        if market == "HK":
            df_balance_sheet = ak.stock_financial_hk_report_em(
                stock=stock_code, 
                symbol="资产负债表", 
                indicator=period
            )
        elif market == "A":
            df_balance_sheet = ak.stock_balance_sheet_by_yearly_em(symbol=stock_code)
        else:
            raise ValueError(f"不支持的市场类型: {market}，请使用 'HK' 或 'A'")
        
        if verbose:
            print(f"成功获取资产负债表，共 {len(df_balance_sheet)} 行数据")
            print(f"数据列名: {list(df_balance_sheet.columns)}")
            print("\n数据预览:")
            print(df_balance_sheet.head())
        
        return df_balance_sheet
    
    except Exception as e:
        if verbose:
            print(f"获取资产负债表失败: {e}")
        return None


def get_income_statement(stock_code: str = "00020", market: str = "HK", period: str = "年度", verbose: bool = False) -> Optional[pd.DataFrame]:
    """    获取公司的利润表
    
    Args:
        stock_code (str): 股票代码
        market (str): 股票市场，"HK"为港股，"A"为A股
        period (str): 报告期间，可选"年度"或"中期"（仅港股适用），默认为"年度"
        verbose (bool): 是否打印详细信息，默认为False
    
    Returns:
        pd.DataFrame: 利润表数据，如果获取失败则返回None
    """
    try:
        if verbose:
            print(f"正在获取{market}股票代码 {stock_code} 的{period}利润表...")
            
        stock_code = standardize_symbol(stock_code)
        
        if market == "HK":
            df_income_statement = ak.stock_financial_hk_report_em(
                stock=stock_code, 
                symbol="利润表", 
                indicator=period
            )
        elif market == "A":
            df_income_statement = ak.stock_profit_sheet_by_yearly_em(symbol=stock_code)
        else:
            raise ValueError(f"不支持的市场类型: {market}，请使用 'HK' 或 'A'")
        
        if verbose:
            print(f"成功获取利润表，共 {len(df_income_statement)} 行数据")
            print(f"数据列名: {list(df_income_statement.columns)}")
            print("\n数据预览:")
            print(df_income_statement.head())
        
        return df_income_statement
    
    except Exception as e:
        if verbose:
            print(f"获取利润表失败: {e}")
        return None


def get_cash_flow_statement(stock_code: str = "00020", market: str = "HK", period: str = "年度", verbose: bool = False) -> Optional[pd.DataFrame]:
    """
    获取公司的现金流量表
    
    Args:
        stock_code (str): 股票代码
        market (str): 股票市场，"HK"为港股，"A"为A股
        period (str): 报告期间，可选"年度"或"中期"（仅港股适用），默认为"年度"
        verbose (bool): 是否打印详细信息，默认为True
    
    Returns:
        pd.DataFrame: 现金流量表数据，如果获取失败则返回None
    """
    try:
        if verbose:
            print(f"正在获取{market}股票代码 {stock_code} 的{period}现金流量表...")
            
        stock_code = standardize_symbol(stock_code)
        
        if market == "HK":
            df_cash_flow = ak.stock_financial_hk_report_em(
                stock=stock_code, 
                symbol="现金流量表", 
                indicator=period
            )
        elif market == "A":
            df_cash_flow = ak.stock_cash_flow_sheet_by_yearly_em(symbol=stock_code)
        else:
            raise ValueError(f"不支持的市场类型: {market}，请使用 'HK' 或 'A'")
        
        if verbose:
            print(f"成功获取现金流量表，共 {len(df_cash_flow)} 行数据")
            print(f"数据列名: {list(df_cash_flow.columns)}")
            print("\n数据预览:")
            print(df_cash_flow.head())
        
        return df_cash_flow
    
    except Exception as e:
        if verbose:
            print(f"获取现金流量表失败: {e}")
        return None

def save_single_financial_statement(df: Optional[pd.DataFrame],
                                   statement_type: str,
                                   stock_code: str = "00020",
                                   market: str = "HK",
                                   period: str = "年度",
                                   company_name: str = None,
                                   save_dir: str = ".") -> None:
    """
    保存单个财务报表（现金流量表、利润表或资产负债表）为CSV文件
    
    Args:
        df (pd.DataFrame): 财务报表数据
        statement_type (str): 报表类型，如 '现金流量表'、'利润表'、'资产负债表'
        stock_code (str): 股票代码，用于文件命名
        market (str): 股票市场，"HK"为港股，"A"为A股
        period (str): 报告期间，用于文件命名
        company_name (str): 公司名称，用于文件命名，如果为None则只使用股票代码
        save_dir (str): 保存文件的目录，默认为当前目录
    """
    
    save_dir = os.path.join("..","datasets", "company_financial_statements")
    os.makedirs(save_dir, exist_ok=True)
    if df is not None:
        if company_name:
            filename = f"{company_name}_{market}_{stock_code}_{statement_type}_{period}.csv"
        else:
            filename = f"{market}_{stock_code}_{statement_type}_{period}.csv"
        filepath = os.path.join(save_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"已保存 {statement_type} 到文件: {filepath}")
    else:
        print(f"跳过保存 {statement_type}，因为数据获取失败")


if __name__ == "__main__":
    # 如果直接运行此脚本，则执行获取语料库函数
    # get_knowledgebase()
    
    # # 测试获取股票新闻
    # print("测试获取股票新闻...")
    # news_list = get_stock_news("300059", max_news=5)  # 获取东方财富的5条新闻
    # print(f"获取到 {len(news_list)} 条新闻")
    
    # 测试获取股票价格数据
    # print("测试获取股票价格数据...")
    # price_data = get_price("300059", start_date="2025-01-01")  # 不指定end_date，自动使用当前日期
    # if price_data:
    #     print(f"成功获取价格数据，共 {price_data['data_count']} 条记录")
    #     print(f"最新数据日期: {price_data['data'][-1]['date']}")
    #     print(f"最新收盘价: {price_data['data'][-1]['close']}")
    # else:
    #     print("获取价格数据失败")
    
     # 获取特定股票的行情并保存（支持不带前缀的代码）
    # stock_list = ['300059']  # 浦发银行和平安银行
    # specific_stocks = get_realtime_quotes(stock_list)
    
    # 获取涨幅最大的10只股票并保存
    # df = get_realtime_quotes()  # 不传参数获取全市场
    # top_gainers = get_top_stocks(df, 10, '涨跌幅')
    
    # print(" 获取现金流量表:")
    cash_flow = get_cash_flow_statement(stock_code="688256", market="A", period="年度")
    save_single_financial_statement(cash_flow, "现金流量表", "688256", "A", "年度")
    
    
    