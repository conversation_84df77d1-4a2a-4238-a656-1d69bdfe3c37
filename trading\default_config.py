import os

DEFAULT_CONFIG = {
    "project_dir": os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
    "results_dir": os.getenv("TRADINGAGENTS_RESULTS_DIR", "./results"),
    "data_dir": os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
                             "datasets/data_dir"),
    "data_cache_dir": os.path.join(
        os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
        "datasets/data_cache",
    ),
    # LLM settings
    "llm_provider": "openai",
    "deep_think_llm": "doubao-seed-1-6-thinking-250615",
    "quick_think_llm": "doubao-seed-1-6-flash-250715",
    "backend_url": "https://ark.cn-beijing.volces.com/api/v3",
    "api_key": "25e77c63-8424-4b59-b03d-a7facdb8ebf1",
    
    "sentiment_llm": "doubao-seed-1-6-flash-250715",
    "sentiment_url": "https://ark.cn-beijing.volces.com/api/v3",
    "sentiment_api_key": "25e77c63-8424-4b59-b03d-a7facdb8ebf1",
    
    "embedding_llm": "BAAI/bge-m3",
    "embedding_url": "https://api.siliconflow.cn/v1",
    "embedding_api_key": "sk-yasxyjkrvevjwtbvxsxyaulmxwtibwqoidvjfposfnvvuhhk",
    
    # Debate and discussion settings
    "max_debate_rounds": 1,
    "max_risk_discuss_rounds": 1,
    "max_recur_limit": 100,
    # Tool settings
    "online_tools": True,
}
