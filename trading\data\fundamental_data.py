from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
import json
import numpy as np
import logging
from tavily import TavilyClient

# 设置日志记录
logger = logging.getLogger("FinancialDataFetcher")


class FinancialDataFetcher:
    """财务数据获取器，提供统一的数据获取和处理接口"""
    
    def __init__(self):
        self.cache = {}  # 简单的缓存机制
    
    def _get_realtime_data(self, symbol: str) -> Optional[pd.Series]:
        """获取实时行情数据"""
        try:
            logger.info("Fetching real-time quotes...")
            realtime_data = ak.stock_zh_a_spot_em()
            if realtime_data is None or realtime_data.empty:
                logger.warning("No real-time quotes data available")
                return None

            stock_data = realtime_data[realtime_data['代码'] == symbol]
            if stock_data.empty:
                logger.warning(f"No real-time quotes found for {symbol}")
                return None

            logger.info("✓ Real-time quotes fetched")
            return stock_data.iloc[0]
        except Exception as e:
            logger.error(f"Error fetching real-time data: {e}")
            return None
    
    def _get_financial_indicators(self, symbol: str) -> Optional[pd.Series]:
        """获取财务指标数据"""
        try:
            logger.info("Fetching Sina financial indicators...")
            current_year = datetime.now().year
            financial_data = ak.stock_financial_analysis_indicator(
                symbol=symbol, start_year=str(current_year-1))
            
            if financial_data is None or financial_data.empty:
                logger.warning("No financial indicator data available")
                return None

            # 按日期排序并获取最新的数据
            financial_data['日期'] = pd.to_datetime(financial_data['日期'])
            financial_data = financial_data.sort_values('日期', ascending=False)
            latest_financial = financial_data.iloc[0] if not financial_data.empty else pd.Series()
            
            logger.info(f"✓ Financial indicators fetched ({len(financial_data)} records)")
            logger.info(f"Latest data date: {latest_financial.get('日期')}")
            return latest_financial
        except Exception as e:
            logger.error(f"Error fetching financial indicators: {e}")
            return None
    
    def _get_financial_report(self, symbol: str, report_type: str) -> Tuple[Optional[pd.Series], Optional[pd.Series]]:
        """获取财务报表数据
        
        Args:
            symbol: 股票代码
            report_type: 报表类型 ('利润表', '资产负债表', '现金流量表')
            
        Returns:
            Tuple[最新期数据, 上一期数据]
        """
        try:
            logger.info(f"Fetching {report_type}...")
            report_data = ak.stock_financial_report_sina(
                stock=f"sh{symbol}", symbol=report_type)
            
            if not report_data.empty:
                latest_data = report_data.iloc[0]
                previous_data = report_data.iloc[1] if len(report_data) > 1 else report_data.iloc[0]
                logger.info(f"✓ {report_type} fetched")
                return latest_data, previous_data
            else:
                logger.warning(f"Failed to get {report_type}")
                return None, None
        except Exception as e:
            logger.error(f"Error getting {report_type}: {e}")
            return None, None
    
    @staticmethod
    def _convert_percentage(value: float) -> float:
        """将百分比值转换为小数"""
        try:
            return float(value) / 100.0 if value is not None else 0.0
        except:
            return 0.0
    
    @staticmethod
    def _safe_float(value, default: float = 0.0) -> float:
        """安全转换为浮点数"""
        try:
            return float(value) if value is not None else default
        except:
            return default


def get_financial_metrics(symbol: str) -> Dict[str, Any]:
    """获取财务指标数据"""
    logger.info(f"Getting financial indicators for {symbol}...")
    
    fetcher = FinancialDataFetcher()
    
    try:
        # 获取实时行情数据
        stock_data = fetcher._get_realtime_data(symbol)
        if stock_data is None:
            return [{}]
        
        # 获取财务指标数据
        latest_financial = fetcher._get_financial_indicators(symbol)
        if latest_financial is None:
            return [{}]
        
        # 获取利润表数据
        latest_income, _ = fetcher._get_financial_report(symbol, "利润表")
        if latest_income is None:
            latest_income = pd.Series()
        
        # 构建完整指标数据
        logger.info("Building indicators...")
        try:
            all_metrics = {
                # 市场数据
                "market_cap": fetcher._safe_float(stock_data.get("总市值", 0)),
                "float_market_cap": fetcher._safe_float(stock_data.get("流通市值", 0)),

                # 盈利数据
                "revenue": fetcher._safe_float(latest_income.get("营业总收入", 0)),
                "net_income": fetcher._safe_float(latest_income.get("净利润", 0)),
                "return_on_equity": fetcher._convert_percentage(latest_financial.get("净资产收益率(%)", 0)),
                "net_margin": fetcher._convert_percentage(latest_financial.get("销售净利率(%)", 0)),
                "operating_margin": fetcher._convert_percentage(latest_financial.get("营业利润率(%)", 0)),

                # 增长指标
                "revenue_growth": fetcher._convert_percentage(latest_financial.get("主营业务收入增长率(%)", 0)),
                "earnings_growth": fetcher._convert_percentage(latest_financial.get("净利润增长率(%)", 0)),
                "book_value_growth": fetcher._convert_percentage(latest_financial.get("净资产增长率(%)", 0)),

                # 财务健康指标
                "current_ratio": fetcher._safe_float(latest_financial.get("流动比率", 0)),
                "debt_to_equity": fetcher._convert_percentage(latest_financial.get("资产负债率(%)", 0)),
                "free_cash_flow_per_share": fetcher._safe_float(latest_financial.get("每股经营性现金流(元)", 0)),
                "earnings_per_share": fetcher._safe_float(latest_financial.get("加权每股收益(元)", 0)),

                # 估值比率
                "pe_ratio": fetcher._safe_float(stock_data.get("市盈率-动态", 0)),
                "price_to_book": fetcher._safe_float(stock_data.get("市净率", 0)),
                "price_to_sales": (fetcher._safe_float(stock_data.get("总市值", 0)) / 
                                 fetcher._safe_float(latest_income.get("营业总收入", 1))) 
                                 if fetcher._safe_float(latest_income.get("营业总收入", 0)) > 0 else 0,
            }

            # 只返回 agent 需要的指标
            agent_metrics = {
                # 盈利能力指标
                "return_on_equity": all_metrics["return_on_equity"],
                "net_margin": all_metrics["net_margin"],
                "operating_margin": all_metrics["operating_margin"],

                # 增长指标
                "revenue_growth": all_metrics["revenue_growth"],
                "earnings_growth": all_metrics["earnings_growth"],
                "book_value_growth": all_metrics["book_value_growth"],

                # 财务健康指标
                "current_ratio": all_metrics["current_ratio"],
                "debt_to_equity": all_metrics["debt_to_equity"],
                "free_cash_flow_per_share": all_metrics["free_cash_flow_per_share"],
                "earnings_per_share": all_metrics["earnings_per_share"],

                # 估值比率
                "pe_ratio": all_metrics["pe_ratio"],
                "price_to_book": all_metrics["price_to_book"],
                "price_to_sales": all_metrics["price_to_sales"],
            }

            logger.info("✓ Indicators built successfully")

            # 打印调试信息
            logger.debug("\n获取到的完整指标数据：")
            for key, value in all_metrics.items():
                logger.debug(f"{key}: {value}")

            logger.debug("\n传递给 agent 的指标数据：")
            for key, value in agent_metrics.items():
                logger.debug(f"{key}: {value}")

            return [agent_metrics]

        except Exception as e:
            logger.error(f"Error building indicators: {e}")
            return [{}]

    except Exception as e:
        logger.error(f"Error getting financial indicators: {e}")
        return [{}]


def get_financial_statements(symbol: str) -> Dict[str, Any]:
    """获取财务报表数据"""
    logger.info(f"Getting financial statements for {symbol}...")
    
    fetcher = FinancialDataFetcher()
    
    try:
        # 获取三大财务报表数据
        latest_balance, previous_balance = fetcher._get_financial_report(symbol, "资产负债表")
        latest_income, previous_income = fetcher._get_financial_report(symbol, "利润表")
        latest_cash_flow, previous_cash_flow = fetcher._get_financial_report(symbol, "现金流量表")
        
        # 如果获取失败，使用空Series
        if latest_balance is None:
            latest_balance = previous_balance = pd.Series()
        if latest_income is None:
            latest_income = previous_income = pd.Series()
        if latest_cash_flow is None:
            latest_cash_flow = previous_cash_flow = pd.Series()

        # 构建财务数据
        line_items = []
        try:
            # 处理最新期间数据
            current_item = {
                # 从利润表获取
                "net_income": fetcher._safe_float(latest_income.get("净利润", 0)),
                "operating_revenue": fetcher._safe_float(latest_income.get("营业总收入", 0)),
                "operating_profit": fetcher._safe_float(latest_income.get("营业利润", 0)),

                # 从资产负债表计算营运资金
                "working_capital": (fetcher._safe_float(latest_balance.get("流动资产合计", 0)) - 
                                  fetcher._safe_float(latest_balance.get("流动负债合计", 0))),

                # 从现金流量表获取
                "depreciation_and_amortization": fetcher._safe_float(
                    latest_cash_flow.get("固定资产折旧、油气资产折耗、生产性生物资产折旧", 0)),
                "capital_expenditure": abs(fetcher._safe_float(
                    latest_cash_flow.get("购建固定资产、无形资产和其他长期资产支付的现金", 0))),
                "free_cash_flow": (fetcher._safe_float(latest_cash_flow.get("经营活动产生的现金流量净额", 0)) - 
                                 abs(fetcher._safe_float(latest_cash_flow.get("购建固定资产、无形资产和其他长期资产支付的现金", 0))))
            }
            line_items.append(current_item)
            logger.info("✓ Latest period data processed successfully")

            # 处理上一期间数据
            previous_item = {
                "net_income": fetcher._safe_float(previous_income.get("净利润", 0)),
                "operating_revenue": fetcher._safe_float(previous_income.get("营业总收入", 0)),
                "operating_profit": fetcher._safe_float(previous_income.get("营业利润", 0)),
                "working_capital": (fetcher._safe_float(previous_balance.get("流动资产合计", 0)) - 
                                  fetcher._safe_float(previous_balance.get("流动负债合计", 0))),
                "depreciation_and_amortization": fetcher._safe_float(
                    previous_cash_flow.get("固定资产折旧、油气资产折耗、生产性生物资产折旧", 0)),
                "capital_expenditure": abs(fetcher._safe_float(
                    previous_cash_flow.get("购建固定资产、无形资产和其他长期资产支付的现金", 0))),
                "free_cash_flow": (fetcher._safe_float(previous_cash_flow.get("经营活动产生的现金流量净额", 0)) - 
                                 abs(fetcher._safe_float(previous_cash_flow.get("购建固定资产、无形资产和其他长期资产支付的现金", 0))))
            }
            line_items.append(previous_item)
            logger.info("✓ Previous period data processed successfully")

        except Exception as e:
            logger.error(f"Error processing financial data: {e}")
            default_item = {
                "net_income": 0,
                "operating_revenue": 0,
                "operating_profit": 0,
                "working_capital": 0,
                "depreciation_and_amortization": 0,
                "capital_expenditure": 0,
                "free_cash_flow": 0
            }
            line_items = [default_item, default_item]

        return line_items

    except Exception as e:
        logger.error(f"Error getting financial statements: {e}")
        default_item = {
            "net_income": 0,
            "operating_revenue": 0,
            "operating_profit": 0,
            "working_capital": 0,
            "depreciation_and_amortization": 0,
            "capital_expenditure": 0,
            "free_cash_flow": 0
        }
        return [default_item, default_item]
