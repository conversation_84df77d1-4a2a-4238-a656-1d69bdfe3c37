

# Create a custom config
from trading.default_config import DEFAULT_CONFIG
from trading.src.graph_framework import TradingAgentsGraph


config = DEFAULT_CONFIG.copy()
config["max_debate_rounds"] = 1  # Increase debate rounds
config["online_tools"] = True  # Increase debate rounds

# Initialize with custom config
ta = TradingAgentsGraph(debug=True, config=config)

# forward propagate
final_state = ta.propagate("平安银行","000001", "2025-07-31")

investment_plan = final_state.get("investment_plan", "")
print("调查讨论状态:")
print(investment_plan)
print("\n" + "="*50 + "\n")

# 输出最终状态
# print("最终状态:")
# print(final_state)