

# Create a custom config
from trading.default_config import DEFAULT_CONFIG
from trading.src.graph_framework import TradingAgentsGraph


config = DEFAULT_CONFIG.copy()
config["max_debate_rounds"] = 1  # Increase debate rounds
config["online_tools"] = True  # Increase debate rounds

# Initialize with custom config
ta = TradingAgentsGraph(debug=True, config=config)

# forward propagate
final_state = ta.propagate("平安银行","000001", "2025-07-31")

investment_plan = final_state.get("investment_plan", "")
print("调查讨论状态:")
print(f"investment_plan长度: {len(investment_plan)}")
print(f"investment_plan内容: {repr(investment_plan[:200])}...")  # 显示前200个字符
print(investment_plan)
print("\n" + "="*50 + "\n")

# 调试：查看final_state的所有键
print("final_state的所有键:")
for key in final_state.keys():
    if isinstance(final_state[key], str):
        print(f"  {key}: {len(final_state[key])} 字符")
    else:
        print(f"  {key}: {type(final_state[key])}")
print("\n" + "="*30 + "\n")

# 输出最终状态
# print("最终状态:")
# print(final_state)